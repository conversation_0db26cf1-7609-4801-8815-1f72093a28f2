<?xml version="1.0" encoding="UTF-8" ?>
<cache xmlns="http://maven.apache.org/BUILD-CACHE-CONFIG/1.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://maven.apache.org/BUILD-CACHE-CONFIG/1.0.0 https://maven.apache.org/xsd/build-cache-config-1.0.0.xsd">

    <configuration>
        <enabled>true</enabled>
        <hashAlgorithm>SHA-256</hashAlgorithm>
        <validateXml>true</validateXml>
        <remote enabled="false"/>
        <local>
            <maxBuildsCached>10</maxBuildsCached>
        </local>
    </configuration>

    <input>
        <global>
            <glob>
                <pattern>**/pom.xml</pattern>
                <pattern>**/*.properties</pattern>
                <pattern>**/*.yml</pattern>
                <pattern>**/*.yaml</pattern>
            </glob>
        </global>
    </input>

    <executionControl>
        <runAlways>
            <plugins>
                <plugin artifactId="maven-clean-plugin"/>
                <plugin artifactId="maven-deploy-plugin"/>
                <plugin artifactId="maven-install-plugin"/>
            </plugins>
        </runAlways>
        
        <reconcile>
            <plugins>
                <plugin artifactId="maven-compiler-plugin"/>
                <plugin artifactId="maven-resources-plugin"/>
                <plugin artifactId="maven-surefire-plugin"/>
                <plugin artifactId="frontend-maven-plugin"/>
            </plugins>
        </reconcile>
    </executionControl>

</cache>
