@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700&display=swap');

/* Import mobile-specific styles */
@import '../styles/mobile.css';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    
    /* Custom UAV System Variables */
    --uav-primary: 210 29% 24%;
    --uav-secondary: 204 70% 53%;
    --uav-accent: 0 74% 58%;
    --uav-success: 142 71% 45%;
    --uav-warning: 38 92% 50%;
    --uav-danger: 0 74% 58%;
    
    /* Font Variables */
    --font-inter: 'Inter', sans-serif;
    --font-orbitron: 'Orbitron', monospace;
    --font-mono: 'JetBrains Mono', monospace;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-orbitron;
  }
}

@layer components {
  /* UAV System Specific Components */
  .uav-gradient {
    @apply bg-gradient-to-br from-uav-secondary to-uav-primary;
  }
  
  .status-authorized {
    @apply bg-green-100 text-green-800 border-green-200;
  }
  
  .status-unauthorized {
    @apply bg-red-100 text-red-800 border-red-200;
  }
  
  .status-hibernating {
    @apply bg-purple-100 text-purple-800 border-purple-200;
  }
  
  .status-charging {
    @apply bg-yellow-100 text-yellow-800 border-yellow-200;
  }
  
  .status-maintenance {
    @apply bg-orange-100 text-orange-800 border-orange-200;
  }
  
  .status-emergency {
    @apply bg-red-200 text-red-900 border-red-300;
  }
  
  /* Custom scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) hsl(var(--muted));
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-muted;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground rounded-full;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-foreground;
  }
  
  /* Loading animations */
  .loading-pulse {
    @apply animate-pulse bg-muted;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-muted border-t-primary;
  }
  
  /* Glass morphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-black/20;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Focus visible utilities */
  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }
}

/* Leaflet map styles override */
.leaflet-container {
  @apply rounded-lg overflow-hidden;
}

.leaflet-popup-content-wrapper {
  @apply rounded-lg shadow-lg;
}

.leaflet-popup-content {
  @apply text-sm;
}

/* Chart.js responsive container */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* Custom animations for UAV system */
@keyframes uav-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.uav-pulse {
  animation: uav-pulse 2s ease-in-out infinite;
}

/* Status indicator animations */
@keyframes status-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.status-blink {
  animation: status-blink 1s ease-in-out infinite;
}

/* Real-time data update animation */
@keyframes data-update {
  0% { background-color: hsl(var(--primary) / 0.1); }
  50% { background-color: hsl(var(--primary) / 0.3); }
  100% { background-color: transparent; }
}

.data-update {
  animation: data-update 0.5s ease-out;
}
